import { Stack, StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";

import { Environment } from "../cloud-config/config";
import { servers } from "../cloud-config/default/servers";
import { ECRStack } from "./ecr-stack";
import { GithubActionsRoleStack } from "./github-actions-role-stack";
import { DiscoveryApiStack } from "./services/discovery-api/discovery-api-stack";
import { LocalCertsStack } from "./local-certs-stack";
import { RootHostedZoneStack } from "./root-hosted-zone";

export interface EntryStackProps extends StackProps {
  pullAccountIds?: string[];
  environments?: Environment[];
}

export class EntryStack extends Stack {
  constructor(scope: Construct, id: string, props: EntryStackProps) {
    super(scope, id, props);

    // Extract account IDs from environmentInfos
    const pullAccountIds = props.environments?.map(env => env.accountId) || props.pullAccountIds;

    // create an ECR stack for each server
    for (const serverName of Object.keys(servers)) {
      new ECRStack(this, serverName, {
        pullAccountIds: pullAccountIds,
      });
    }

    // and some extras for the sensor base images
    new ECRStack(this, 'hero-core/sensors-build-base', {
      pullAccountIds: pullAccountIds,
    });

    new ECRStack(this, 'hero-core/sensors-runtime-base', {
      pullAccountIds: pullAccountIds,
    });

    new GithubActionsRoleStack(this, 'GithubActionsRoleStack', {
      githubOrg: 'herosafety',
      githubRepo: 'hero-core',
      githubBranch: 'main',
    });

    // create the root hosted zone
    const rootHostedZoneStack = new RootHostedZoneStack(this, 'RootHostedZone', {
      environments: props.environments,
      env: props.env,
      crossRegionReferences: true,
    });

    const rootDomain = 'gethero.com';

    // create local certificates stack
    const localCertsStack = new LocalCertsStack(this, 'LocalCertsStack', {
      hostedZone: rootHostedZoneStack.publicHostedZone,
      rootDomain: rootDomain,
      env: props.env,
      crossRegionReferences: true,
    });

    // create Discovery API service stack
    new DiscoveryApiStack(this, 'DiscoveryApiStack', {
      rootDomain: rootDomain,
      wildcardApiCert: localCertsStack.wildcardApiCert,
      hostedZone: rootHostedZoneStack.publicHostedZone,
      env: props.env,
      crossRegionReferences: true,
    });
  }
}