# Discovery API

This API provides email-to-environment discovery for mobile authentication via API Gateway.

## Purpose

Maps email domains to Cognito User Pool configurations, enabling a single mobile app build to authenticate across multiple environments (demo-1, TCU, etc.).

## API Endpoints

### Primary: API Gateway

**URL**: https://discovery.api.gethero.com  
**Method**: POST /  
**Protection**: WAF rate limiting (500 requests per 5 minutes per IP)

**Headers**:

- `Content-Type: application/json`

**Request**:

```json
{
  "email": "<EMAIL>"
}
```

**Response**:

```json
{
  "userPoolUrl": "https://auth.tcu.gethero.com",
  "clientId": "3fv7mur82c02q30o7oflj0u5ub"
}
```

**Error Response**:

```json
{
  "error": "No configuration found for domain: unknown.com"
}
```

### Health Check

**URL**: https://discovery.api.gethero.com/health  
**Method**: GET  
**Response**: `{}` (200 OK)

## Domain Mapping

Currently hardcoded in `main.go`:

- `tcu.edu` → TCU environment
- `gethero.com` → demo-1 environment
- `demo-1.gethero.com` → demo-1 environment

## Security & Monitoring

- **WAF Protection**: Rate limiting (500 requests per 5 minutes per IP)
- **CORS**: Configured for mobile app origins
- **CloudWatch Alarms**:
  - 5xx Errors: Alert on 3+ errors in 10 minutes
  - High Latency: Alert on P95 > 1000ms
  - WAF Blocks: Alert on 50+ blocked requests in 5 minutes
- **SNS Alerts**: discovery-api-alerts topic for monitoring
- **Access Logging**: Structured JSON logs in CloudWatch

## Building & Deployment

```bash
# Build all lambdas (including discovery lambda)
./infra/scripts/prepare-cdk.sh

# Deploy via CDK (from the infra directory)
CDK_ENV=shared cdk deploy --profile shared-services --concurrency 10 --require-approval never CDKSharedEntryStack/DiscoveryApiStack --exclusively
```

(set up the -profile of shared-services under your ~/.aws/config)

## Testing

```bash
# Test the API Gateway endpoint (primary)
curl -X POST https://discovery.api.gethero.com \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Test health check
curl https://discovery.api.gethero.com/health

# Test with different domains
curl -X POST https://discovery.api.gethero.com \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Test WAF rate limiting (will get blocked after 500 requests in 5 minutes)
# for i in {1..600}; do curl -X POST https://discovery.api.gethero.com -H "Content-Type: application/json" -d '{"email": "<EMAIL>"}'; done
```

## Monitoring

### CloudWatch Dashboards

- **API Gateway**: View request count, latency, errors
- **WAF**: View blocked/allowed requests
- **Lambda**: View function performance

### Alarm States

Check alarm status in AWS Console → CloudWatch → Alarms:

- `Discovery-API-5xx-Errors-gethero.com`
- `Discovery-API-High-Latency-gethero.com`
- `Discovery-API-WAF-Blocked-gethero.com`

### Logs

- **API Gateway Access Logs**: `/aws/apigateway/discovery-api-gethero.com`
- **Lambda Logs**: `/aws/lambda/shared-discovery-lambda`
