import * as cdk from 'aws-cdk-lib';
import * as apigwv2 from 'aws-cdk-lib/aws-apigatewayv2';
import { HttpLambdaIntegration } from 'aws-cdk-lib/aws-apigatewayv2-integrations';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53targets from 'aws-cdk-lib/aws-route53-targets';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as snsSubscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import { Construct } from 'constructs';
import * as path from 'path';

export interface DiscoveryApiStackProps extends cdk.StackProps {
    rootDomain: string;
    wildcardApiCert: acm.ICertificate;
    hostedZone: route53.IHostedZone;
}

export class DiscoveryApiStack extends cdk.Stack {
    public readonly discoveryApi: apigwv2.HttpApi;
    public readonly discoveryApiDomain: apigwv2.DomainName;
    
    constructor(scope: Construct, id: string, props: DiscoveryApiStackProps) {
        super(scope, id, props);

        const { rootDomain, wildcardApiCert, hostedZone } = props;

        // Discovery API Stack - Complete service boundary including:
        // - Lambda functions (discovery + health check)
        // - API Gateway + custom domain + WAF
        // - CloudWatch monitoring + alarms
        // - SNS alerting for PagerDuty integration


        // Discovery Lambda for mobile multi-tenant auth
        const discoveryLambda = new lambda.Function(this, 'DiscoveryLambda', {
            functionName: 'shared-discovery-lambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            timeout: cdk.Duration.seconds(10),
            code: lambda.Code.fromAsset(path.join(__dirname, 'discovery-lambda/bootstrap.zip')),
            environment: {
                PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL: 'debug',
                PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: '10',
                SECRETS_MANAGER_TTL: '300',
                // Structured logging for better root cause analysis
                LOG_LEVEL: 'INFO',
                LOG_FORMAT: 'JSON',
                SERVICE_NAME: 'discovery-api',
                SERVICE_VERSION: '1.0.0',
            },
            paramsAndSecrets: lambda.ParamsAndSecretsLayerVersion.fromVersion(
                lambda.ParamsAndSecretsVersions.V1_0_103,
                {
                    cacheSize: 10,
                    logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
                }
            ),
        });


        // Custom domain for discovery API  
        const discoveryApiDomain = new apigwv2.DomainName(this, 'DiscoveryApiDomain', {
            domainName: `discovery.api.${rootDomain}`,
            certificate: wildcardApiCert,
        });

        // HTTP API Gateway for discovery service
        const discoveryApi = new apigwv2.HttpApi(this, 'DiscoveryApi', {
            apiName: 'discovery-api',
            description: 'Discovery API for mobile multi-tenant authentication',
            defaultDomainMapping: {
                domainName: discoveryApiDomain,
            },
            corsPreflight: {
                allowHeaders: ['Content-Type', 'X-Api-Key', 'Authorization'],
                allowMethods: [
                    apigwv2.CorsHttpMethod.POST,
                    apigwv2.CorsHttpMethod.OPTIONS
                ],
                allowOrigins: ['*'], // Will be restricted in production
                maxAge: cdk.Duration.hours(1),
            },
        });

        // Lambda integration for the discovery API
        const discoveryLambdaIntegration = new HttpLambdaIntegration(
            'DiscoveryLambdaIntegration',
            discoveryLambda,
            {
                payloadFormatVersion: apigwv2.PayloadFormatVersion.VERSION_2_0,
            }
        );

        // Add POST / route
        discoveryApi.addRoutes({
            path: '/',
            methods: [apigwv2.HttpMethod.POST],
            integration: discoveryLambdaIntegration,
        });

        // Health check Lambda (lightweight, fast response)
        const healthCheckLambda = new lambda.Function(this, 'HealthCheckLambda', {
            functionName: 'shared-discovery-health-check',
            runtime: lambda.Runtime.NODEJS_20_X,
            handler: 'index.handler',
            timeout: cdk.Duration.seconds(3),
            memorySize: 128, // Minimal memory for fast startup
            code: lambda.Code.fromInline(`
                exports.handler = async (event) => {
                    return {
                        statusCode: 200,
                        headers: {
                            'Content-Type': 'application/json',
                            'Cache-Control': 'no-cache'
                        },
                        body: JSON.stringify({})
                    };
                };
            `),
        });

        // Health check integration
        const healthCheckIntegration = new HttpLambdaIntegration(
            'HealthCheckIntegration',
            healthCheckLambda
        );

        // Add GET /health route
        discoveryApi.addRoutes({
            path: '/health',
            methods: [apigwv2.HttpMethod.GET],
            integration: healthCheckIntegration,
        });

        // CloudWatch Log Group for API Gateway access logs
        const apiLogGroup = new logs.LogGroup(this, 'DiscoveryApiLogGroup', {
            logGroupName: `/aws/apigateway/discovery-api-${rootDomain}`,
            retention: logs.RetentionDays.ONE_WEEK,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
        });

        // Configure API Gateway stage access logging with enhanced format for root cause analysis
        const cfnStage = discoveryApi.defaultStage!.node.defaultChild as apigwv2.CfnStage;
        cfnStage.accessLogSettings = {
            destinationArn: apiLogGroup.logGroupArn,
            format: JSON.stringify({
                requestId: '$context.requestId',
                requestTime: '$context.requestTime',
                httpMethod: '$context.httpMethod',
                resourcePath: '$context.path',
                status: '$context.status',
                responseLength: '$context.responseLength',
                responseLatency: '$context.responseLatency',
                integrationLatency: '$context.integrationLatency',
                integrationStatus: '$context.integrationStatus',
                integrationErrorMessage: '$context.integrationErrorMessage',
                errorMessage: '$context.error.message',
                errorMessageString: '$context.error.messageString',
                sourceIp: '$context.identity.sourceIp',
                userAgent: '$context.identity.userAgent',
                caller: '$context.identity.caller',
                user: '$context.identity.user',
                protocol: '$context.protocol',
                domainName: '$context.domainName',
                stage: '$context.stage',
            }),
        };

        // TODO: Add WAF protection later - HTTP API v2 WAF association has compatibility issues

        // SNS Topic for Discovery API alerts and monitoring
        const discoveryApiAlertsTopic = new sns.Topic(this, 'DiscoveryApiAlertsTopic', {
            topicName: `discovery-api-alerts-${rootDomain.replace(/\./g, '-')}`,
            displayName: 'Discovery API Monitoring Alerts',
        });

        // CloudWatch Alarm: 5xx Error Rate
        const discovery5xxAlarm = new cloudwatch.Alarm(this, 'Discovery5xxErrorAlarm', {
            alarmName: `Discovery-API-5xx-Errors-${rootDomain}`,
            alarmDescription: 'Discovery API experiencing 5xx server errors - indicates Lambda or backend issues affecting mobile authentication',
            metric: new cloudwatch.Metric({
                namespace: 'AWS/ApiGateway',
                metricName: '5XXError',
                dimensionsMap: {
                    ApiName: discoveryApi.httpApiName!,
                },
                statistic: 'Sum',
                period: cdk.Duration.minutes(5),
            }),
            threshold: 3,
            evaluationPeriods: 2,
            datapointsToAlarm: 2,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        });

        // Add alarm actions for ALARM and OK states
        discovery5xxAlarm.addAlarmAction(new actions.SnsAction(discoveryApiAlertsTopic));
        discovery5xxAlarm.addOkAction(new actions.SnsAction(discoveryApiAlertsTopic));

        // CloudWatch Alarm: High Latency (P95)
        const discoveryLatencyAlarm = new cloudwatch.Alarm(this, 'DiscoveryHighLatencyAlarm', {
            alarmName: `Discovery-API-High-Latency-${rootDomain}`,
            alarmDescription: 'Discovery API experiencing high latency (P95 > 2000ms) - mobile users will experience slow authentication',
            metric: new cloudwatch.Metric({
                namespace: 'AWS/ApiGateway',
                metricName: 'Latency',
                dimensionsMap: {
                    ApiName: discoveryApi.httpApiName!,
                },
                statistic: 'p95',
                period: cdk.Duration.minutes(5),
            }),
            threshold: 2000, // 2000ms for mobile-optimized UX
            evaluationPeriods: 3,
            datapointsToAlarm: 3,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        });

        // Add alarm actions for ALARM and OK states
        discoveryLatencyAlarm.addAlarmAction(new actions.SnsAction(discoveryApiAlertsTopic));
        discoveryLatencyAlarm.addOkAction(new actions.SnsAction(discoveryApiAlertsTopic));

        // TODO: Add WAF blocked requests alarm when WAF is implemented

        // PagerDuty CloudWatch Integration using native SNS constructs
        const pagerDutyIntegrationKeySecret = secretsmanager.Secret.fromSecretNameV2(
            this, 
            'PagerDutyIntegrationKeySecret',
            'shared/team-eng-alarms/pagerduty-integration-key'
        );

        // Securely construct PagerDuty endpoint URL using CfnDynamicReference
        const integrationKeyRef = new cdk.CfnDynamicReference(
            cdk.CfnDynamicReferenceService.SECRETS_MANAGER, 
            `${pagerDutyIntegrationKeySecret.secretArn}:SecretString`
        );
        
        const pagerDutyUrl = cdk.Fn.sub(
            'https://events.pagerduty.com/integration/${IntegrationKey}/enqueue',
            {
                IntegrationKey: integrationKeyRef.toString(),
            }
        );

        // Add native SNS subscription to PagerDuty
        discoveryApiAlertsTopic.addSubscription(
            new snsSubscriptions.UrlSubscription(pagerDutyUrl, {
                protocol: sns.SubscriptionProtocol.HTTPS,
                rawMessageDelivery: false, // Preserve CloudWatch JSON structure for PagerDuty parsing
                filterPolicy: {
                    AlarmName: sns.SubscriptionFilter.existsFilter(), // Only CloudWatch alarms trigger PagerDuty
                },
            })
        );

        // Store API and domain references for use in other stacks
        this.discoveryApi = discoveryApi;
        this.discoveryApiDomain = discoveryApiDomain;

        // Route 53 A-record for custom domain
        new route53.ARecord(this, 'DiscoveryApiARecord', {
            recordName: 'discovery.api',
            zone: hostedZone,
            target: route53.RecordTarget.fromAlias(
                new route53targets.ApiGatewayv2DomainProperties(
                    discoveryApiDomain.regionalDomainName,
                    discoveryApiDomain.regionalHostedZoneId
                )
            ),
            ttl: cdk.Duration.seconds(300), // Fast propagation for deployments
        });

        // Outputs for easy access

        new cdk.CfnOutput(this, 'DiscoveryApiUrl', {
            value: discoveryApi.apiEndpoint,
            description: 'Discovery API Gateway endpoint URL',
            exportName: 'SharedDiscoveryApiUrl',
        });

        new cdk.CfnOutput(this, 'DiscoveryCustomDomainUrl', {
            value: `https://discovery.api.${rootDomain}`,
            description: 'Discovery API custom domain URL',
            exportName: 'SharedDiscoveryCustomDomainUrl',
        });

        new cdk.CfnOutput(this, 'DiscoveryAlertsTopicArn', {
            value: discoveryApiAlertsTopic.topicArn,
            description: 'Discovery API SNS alerts topic ARN for PagerDuty integration',
            exportName: 'SharedDiscoveryAlertsTopicArn',
        });

        new cdk.CfnOutput(this, 'Discovery5xxAlarmArn', {
            value: discovery5xxAlarm.alarmArn,
            description: 'Discovery API 5xx Error CloudWatch Alarm ARN',
            exportName: 'SharedDiscovery5xxAlarmArn',
        });

        new cdk.CfnOutput(this, 'DiscoveryLatencyAlarmArn', {
            value: discoveryLatencyAlarm.alarmArn,
            description: 'Discovery API High Latency CloudWatch Alarm ARN',
            exportName: 'SharedDiscoveryLatencyAlarmArn',
        });

        new cdk.CfnOutput(this, 'PagerDutyIntegrationStatus', {
            value: 'PagerDuty CloudWatch integration configured via native SNS subscription',
            description: 'Discovery API alarms will create/resolve PagerDuty incidents automatically using CDK native constructs',
        });
    }
}