# Task ID: 22
# Title: Configure CloudWatch alarms for monitoring
# Status: done
# Dependencies: 21
# Priority: medium
# Description: Set up CloudWatch alarms for 5xx errors, high latency, and WAF blocks
# Details:
Create Alarm constructs using aws-cdk-lib/aws-cloudwatch. Configure alarms: 1) 5xx errors: metric 'AWS/ApiGateway/5XXError', threshold: 5 errors in 5 minutes, 2) High latency: metric 'AWS/ApiGateway/Latency', threshold: 2000ms P95, 3) WAF blocks: metric 'AWS/WAFV2/BlockedRequests', threshold: 50 blocks in 5 minutes. Set alarm actions to SNS topic if available. Use MetricFilter for custom log-based metrics if needed.

# Test Strategy:
Trigger test conditions to verify alarms fire correctly. Test 5xx alarm by causing Lambda errors, latency alarm by adding artificial delays, WAF alarm by exceeding rate limits. Verify alarm states transition properly.

# Subtasks:
## 1. Define CloudWatch Alarm for 5xx Errors [done]
### Dependencies: None
### Description: Create a CloudWatch Alarm construct in aws-cdk-lib/aws-cloudwatch to monitor the 'AWS/ApiGateway/5XXError' metric with a threshold of 5 errors within 5 minutes.
### Details:
Configure the alarm with the specified metric, threshold, and evaluation period. Ensure the alarm triggers when 5xx errors exceed the threshold in the defined window.

## 2. Define CloudWatch Alarm for High Latency [done]
### Dependencies: None
### Description: Create a CloudWatch Alarm construct to monitor the 'AWS/ApiGateway/Latency' metric, specifically the P95 percentile, with a threshold of 2000ms.
### Details:
Set up the alarm to evaluate the P95 latency metric and trigger if the value exceeds 2000ms within the evaluation period.

## 3. Define CloudWatch Alarm for WAF Blocked Requests [done]
### Dependencies: None
### Description: Create a CloudWatch Alarm construct to monitor the 'AWS/WAFV2/BlockedRequests' metric with a threshold of 50 blocks in 5 minutes.
### Details:
Configure the alarm to trigger when the number of blocked requests by WAF exceeds 50 within a 5-minute window.

## 4. Configure Alarm Actions and SNS Notifications [done]
### Dependencies: 22.1, 22.2, 22.3
### Description: Set up alarm actions to notify an SNS topic when any of the defined alarms are triggered.
### Details:
Associate each alarm with an SNS topic for notifications. Ensure the SNS topic exists and is properly configured for alarm actions.

## 5. Implement Custom MetricFilters for Log-Based Metrics (if needed) [done]
### Dependencies: None
### Description: Create MetricFilters using aws-cdk-lib/aws-logs for any custom log-based metrics required for monitoring, and integrate them with CloudWatch alarms as necessary.
### Details:
Define MetricFilters to extract relevant metrics from CloudWatch Logs, and connect these metrics to new or existing alarms if default metrics are insufficient.

