# Task ID: 23
# Title: Update mobile app configuration for new endpoint
# Status: pending
# Dependencies: 22
# Priority: high
# Description: Modify mobile app configuration to use new discovery.api.<rootDomain> endpoint for API gateway testing
# Details:
Update mobile app constants: export const DISCOVERY_URL = 'https://discovery.api.gethero.com'; Add exponential backoff for 429 responses: initial delay 1s, max delay 30s, max retries 3. Use fetch API with timeout: 10 seconds. Handle errors gracefully with user-friendly messages. This is for direct API gateway testing without fallback mechanisms.

# Test Strategy:
Test mobile app with new endpoint in staging environment. Test rate limiting handling with proper retry behavior. Validate error handling and user experience with the new API gateway endpoint.

# Subtasks:
## 1. Update Mobile App Constants for New Endpoint [pending]
### Dependencies: None
### Description: Modify the mobile app configuration to set DISCOVERY_URL to the new endpoint (https://discovery.api.<rootDomain>).
### Details:
Update the relevant constants or configuration files in the codebase to reference the new discovery API endpoint as the primary URL for direct API gateway testing.

## 2. Add Exponential Backoff for 429 Responses [pending]
### Dependencies: 23.1
### Description: Implement exponential backoff logic for handling HTTP 429 (Too Many Requests) responses, starting with a 1s delay, doubling up to 30s, with a maximum of 3 retries.
### Details:
Detect 429 responses and apply exponential backoff: initial delay 1 second, double each retry, cap at 30 seconds, and stop after 3 attempts.

## 3. Integrate Fetch API Timeout and Error Handling [pending]
### Dependencies: 23.2
### Description: Ensure all fetch requests use a 10-second timeout and handle errors gracefully with user-friendly messages.
### Details:
Wrap fetch calls with timeout logic; display clear, actionable error messages to users for timeouts, network errors, and other failures when communicating with the API gateway.

## 4. Update Discovery Lambda README with API Gateway Information [pending]
### Dependencies: 23.3
### Description: Update the README.md in cloud-shared/services/discovery-api/discovery-lambda/ to reflect the new API Gateway integration, endpoint URLs, testing procedures, and monitoring information.
### Details:
Document the complete API Gateway setup including:
- New endpoint URL: https://discovery.api.gethero.com
- API Gateway integration details and routing
- WAF protection and rate limiting (500 req/5min per IP)
- CloudWatch alarms and monitoring setup
- Testing procedures for both direct Lambda and API Gateway endpoints
- Troubleshooting guide for common issues
- Update deployment instructions to reflect new architecture
- Document the Function URL as legacy/fallback approach

