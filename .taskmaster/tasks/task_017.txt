# Task ID: 17
# Title: Configure custom domain with wildcard certificate
# Status: done
# Dependencies: 16
# Priority: high
# Description: Set up custom domain discovery.api.<rootDomain> using the existing wildcard ACM certificate
# Details:
Use DomainName construct from @aws-cdk/aws-apigatewayv2-alpha. Configure domainName: `discovery.api.${props.rootDomain}`, certificate: props.wildcardApiCert. Create ApiMapping to associate custom domain with HTTP API default stage. Set securityPolicy: SecurityPolicy.TLS_1_2 for compliance. Reference pattern from fargate-service-stack.ts custom domain configuration.

# Test Strategy:
Verify custom domain is created successfully and SSL certificate is properly associated. Test domain resolves correctly and serves HTTPS traffic. Validate certificate chain using SSL testing tools.

# Subtasks:
## 1. Retrieve and reference existing wildcard ACM certificate [done]
### Dependencies: None
### Description: Obtain the existing wildcard ACM certificate and ensure it is accessible within the CDK stack for use with the custom domain.
### Details:
Use aws-cdk-lib/aws-certificatemanager to reference the certificate by ARN (e.g., Certificate.fromCertificateArn). Confirm that the certificate covers discovery.api.<rootDomain> and is validated.

## 2. Configure API Gateway custom domain using DomainName construct [done]
### Dependencies: 17.1
### Description: Set up the custom domain discovery.api.<rootDomain> for the HTTP API using the DomainName construct from @aws-cdk/aws-apigatewayv2-alpha.
### Details:
Instantiate DomainName with domainName: `discovery.api.${props.rootDomain}`, certificate: props.wildcardApiCert, and securityPolicy: SecurityPolicy.TLS_1_2. Reference the configuration pattern from fargate-service-stack.ts.

## 3. Create ApiMapping to associate custom domain with HTTP API default stage [done]
### Dependencies: 17.2
### Description: Map the custom domain to the HTTP API's default stage using ApiMapping.
### Details:
Use the ApiMapping construct to associate the DomainName with the HTTP API's default stage, ensuring requests to the custom domain are routed to the API.

## 5. Validate custom domain and SSL configuration [done]
### Dependencies: None
### Description: Test that the custom domain serves HTTPS traffic with the correct certificate and that the certificate chain is valid.
### Details:
Use curl or SSL testing tools to verify successful HTTPS connections, correct certificate presentation, and valid certificate chain for discovery.api.<rootDomain>.
<info added on 2025-08-04T22:48:02.111Z>
Custom domain implementation completed successfully:

✅ **DomainName Created**: `discovery.api.gethero.com` using wildcard certificate
✅ **API Gateway Integration**: HttpApi configured with defaultDomainMapping
✅ **SSL/TLS Configuration**: Uses existing *.api.gethero.com wildcard certificate
✅ **Build Validation**: TypeScript compilation successful, no linting errors
✅ **Public Interface**: discoveryApiDomain exposed for use in Task 18 (Route 53)
✅ **CDK Outputs**: Custom domain URL exported for easy access

**Implementation Details**:
- Following the exact pattern from fargate-service-stack.ts (lines 269-318)
- DomainName construct properly configured with certificate reference
- API Gateway defaultDomainMapping automatically creates ApiMapping
- Custom domain will be: https://discovery.api.gethero.com/
- Ready for Route 53 A-record creation in Task 18

**Next Step**: Task 18 will create the Route 53 A-record to complete DNS resolution.
</info added on 2025-08-04T22:48:02.111Z>

