# Cellular Call System Architecture

## Overview

The Hero cellular call system is built on **<PERSON><PERSON><PERSON>'s Voice SDK** and provides a comprehensive call management platform with queue-based call routing, hold/resume functionality, and smart wait messaging. The system supports both inbound and outbound calls with real-time state management.

## Core Technologies

- **Twilio Voice SDK**: Primary telephony infrastructure
- **Twilio Voice Client SDK**: Frontend browser-based calling
- **TwiML**: XML-based call flow instructions
- **PostgreSQL**: Call queue and state persistence
- **React Context**: Frontend call state management
- **gRPC/Connect**: Backend service communication

## System Architecture

### Backend Services

#### 1. Communications Service (`services/communications/`)
- **Location**: `services/communications/internal/cellularcall/`
- **Purpose**: Core call management service
- **Key Components**:
  - `usecase/cellularcall_usecase.go`: Business logic layer
  - `data/postgres_callqueue_repo.go`: Database operations
  - `client/twilio.go`: Twilio API client wrapper
  - `twiml/builder.go`: TwiML response generation

#### 2. Call Queue Repository
- **Interface**: `CallQueueRepository` in `data/callqueue_repo.go`
- **Implementations**: PostgreSQL (primary), In-Memory (testing)
- **Operations**: Enqueue, Dequeue, Hold, Resume, End calls

### Frontend Components

#### 1. Call Context (`apps/hero-command-and-control-web/src/app/contexts/Call/`)
- **Purpose**: React context for call state management
- **Features**:
  - Twilio Device initialization and management
  - Token management with auto-refresh
  - Call operation abstractions (accept, hold, resume, end, mute)
  - Real-time queue status monitoring

## Call Flow Architecture

### 1. Inbound Call Flow

```
Customer Call → Twilio Number → Webhook → Queue → Agent Dequeue → Connection
```

**Detailed Steps**:
1. **Call Reception**: Customer dials Twilio number
2. **Webhook Processing**: `/voice` webhook receives call
3. **Queue Placement**: Call placed in Twilio queue with smart wait messaging
4. **Agent Notification**: Queue status updates trigger frontend notifications
5. **Dequeue Process**: Agent accepts call, triggering dequeue operation
6. **Connection**: TwiML connects agent to customer

### 2. Outbound Call Flow

```
Agent Initiate → Device.connect() → TwiML Generation → Twilio Dial → Connection
```

**Detailed Steps**:
1. **Initiation**: Agent enters phone number and initiates call
2. **Validation**: Phone number format validation
3. **Connection**: Twilio Device connects with outbound parameters
4. **TwiML Processing**: Backend generates dial TwiML
5. **Establishment**: Call connected to target number

### 3. Call States

The system maintains five distinct call states:

- **`waiting`**: Call in queue awaiting agent
- **`pending_selective_assignment`**: Call claimed by specific agent but not connected
- **`active`**: Call currently being handled by agent
- **`hold`**: Call placed on hold by agent
- **`ended`**: Call completed (historical tracking)

## Key Components Deep Dive

### 1. Twilio Integration

#### SDK Usage
- **Voice SDK**: `github.com/twilio/twilio-go`
- **Frontend SDK**: `@twilio/voice-sdk` (JavaScript)

#### Authentication
- **Access Tokens**: JWT-based with VoiceGrant permissions
- **Token Refresh**: Automatic refresh before expiry
- **Identity Format**: Asset ID used as Twilio client identity

#### TwiML Generation
Key TwiML builders in `twiml/builder.go`:
- `QueueResponse()`: Places caller in queue with wait URL
- `DequeueResponse()`: Connects agent to next queued call
- `AgentConnectTwiML()`: Connects specific agent to specific customer
- `OutboundDialResponse()`: Initiates outbound calls
- `HoldResponse()`: Places calls on hold with messaging

### 2. Call Queue Management

#### Database Schema (`call_queue` table)
```sql
call_sid TEXT PRIMARY KEY           -- Twilio Call SID
org_id INTEGER                      -- Organization reference
caller TEXT                         -- Customer phone number
caller_name TEXT                    -- Optional caller name
asset_id TEXT                       -- Assigned agent/asset
situation_id TEXT                   -- Associated situation
state TEXT                          -- Call state (waiting/active/hold/ended)
direction TEXT                      -- inbound/outbound
attributes JSONB                    -- Extensible metadata
enqueue_time TIMESTAMPTZ           -- Queue entry time
call_start_time TIMESTAMPTZ        -- Connection time
call_end_time TIMESTAMPTZ          -- End time
last_hold_start TIMESTAMPTZ        -- Hold start time
```

#### Queue Operations
- **Enqueue**: Add incoming calls to waiting state
- **Dequeue**: Assign waiting calls to agents
- **Selective Dequeue**: Agent claims specific call by SID
- **Hold/Resume**: Manage call hold states
- **End**: Mark calls as completed

### 3. Smart Wait Messaging

#### Wait URL System
- **Endpoint**: `/waithold?action=enqueue`
- **Authentication**: Basic auth with org API credentials
- **Messaging**: Dynamic org-specific wait messages
- **Loop Behavior**: Automatic message repetition with pauses

#### Hold Messaging
- **Endpoint**: `/waithold?action=hold`
- **Behavior**: Looping hold messages with redirects
- **Voice**: Polly.Matthew-Neural for natural speech

### 4. Webhook Endpoints

#### Primary Webhooks
- **`/voice`**: Main call routing webhook
- **`/callstatus`**: Call status callbacks (setup but not actively used)
- **`/waithold`**: Smart wait and hold messaging
- **`/twiml/connectAgent`**: Agent-to-customer connection

#### Webhook Security
- **Basic Authentication**: Username:password in URL
- **Dynamic Credentials**: Retrieved from org API users
- **HTTPS Required**: All webhooks use HTTPS

## Frontend Architecture

### 1. Device Management
- **Initialization**: Automatic device setup with token
- **Event Handling**: Registration, errors, incoming calls
- **Audio Control**: Disabled incoming/outgoing sounds
- **Retry Logic**: Automatic reconnection on failures

### 2. Call Operations
- **Accept Next Call**: Dequeue from waiting queue
- **Specific Call Dequeue**: Target specific call by SID
- **Hold/Resume**: Manage call hold states
- **Outbound Calling**: Initiate calls to phone numbers
- **Mute Control**: Toggle microphone state

### 3. State Management
- **Real-time Updates**: 5-second polling for queue status
- **Call Tracking**: Active and held call management
- **Error Handling**: Comprehensive error tracking and user feedback

## Call Forwarding Considerations

### Current Architecture Support
The system has foundational support for call forwarding:

1. **TwiML Flexibility**: Can generate dial instructions to any number
2. **Call Redirection**: `RedirectCall()` method in Twilio client
3. **Dynamic Routing**: Webhook-based call routing decisions
4. **Database Tracking**: Call direction and attributes tracking

### Implementation Approach for Call Forwarding
1. **Configuration**: Add forwarding rules to org configuration
2. **Routing Logic**: Enhance webhook logic to check forwarding rules
3. **TwiML Generation**: Use `OutboundDialResponse()` for forwarded calls
4. **State Tracking**: Track forwarded calls in call queue
5. **Fallback Handling**: Queue calls if forwarding fails

### Required Changes
- **Database**: Add forwarding configuration tables
- **Webhook Logic**: Implement forwarding decision logic
- **TwiML Updates**: Support conditional forwarding responses
- **Frontend**: Admin interface for forwarding configuration

## Monitoring and Observability

### 1. Sentry Integration
- **Performance Tracking**: Call operation timing
- **Error Monitoring**: Comprehensive error capture
- **Custom Metrics**: Call success rates, queue metrics

### 2. Logging
- **Structured Logging**: JSON-formatted logs with context
- **Call Tracing**: Full call lifecycle tracking
- **Debug Information**: TwiML generation and webhook processing

### 3. Health Monitoring
- **Lambda Monitoring**: Twilio error webhook monitoring
- **Device Health**: Frontend device status tracking
- **Queue Metrics**: Real-time queue depth and wait times

## Security

### 1. Authentication
- **Twilio Tokens**: JWT-based with limited TTL
- **Webhook Auth**: Basic authentication with dynamic credentials
- **API Security**: gRPC with authentication middleware

### 2. Data Protection
- **Row Level Security**: PostgreSQL RLS on call_queue table
- **Org Isolation**: All operations scoped to organization
- **Credential Management**: AWS Secrets Manager integration

## Configuration

### Environment Variables
- `TWILIO_ACCOUNT_SID`: Twilio account identifier
- `TWILIO_AUTH_TOKEN`: Twilio authentication token
- `TWILIO_API_KEY_SID/SECRET`: API key for token generation
- `COMMS_SERVER_PUBLIC_DOMAIN`: Public domain for webhooks
- `TWILIO_QUEUE_NAME`: Default queue name

### Database Configuration
- **Queue Configuration**: `twilio_queue_configurations` table
- **Org Settings**: Twilio numbers and app SIDs in `orgs` table
- **Call Persistence**: All call data in `call_queue` table

## Extension Points

### 1. Queue Strategies
- **Interface**: `QueueStrategy` for custom call routing
- **Current**: FIFO (first-in-first-out)
- **Extensible**: Priority-based, skill-based routing

### 2. TwiML Customization
- **Modular Builders**: Separate functions for different call types
- **Voice Configuration**: Polly neural voices
- **Message Customization**: Org-specific messaging

### 3. Webhook Extensions
- **Status Callbacks**: Framework for call event handling
- **Custom Actions**: Extensible action parameter system
- **Error Handling**: Comprehensive error webhook monitoring

This architecture provides a solid foundation for implementing call forwarding by leveraging the existing TwiML generation, webhook routing, and state management systems.
